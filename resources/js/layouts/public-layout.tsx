import { PublicHeader } from '@/components/public-header';
import { PublicFooter } from '@/components/public-footer';
import { type PropsWithChildren } from 'react';

interface PublicLayoutProps {
    title?: string;
    description?: string;
    className?: string;
}

export default function PublicLayout({ 
    children, 
    title, 
    description, 
    className 
}: PropsWithChildren<PublicLayoutProps>) {
    return (
        <div className="flex min-h-screen flex-col">
            <PublicHeader />
            <main className={`flex-1 ${className || ''}`}>
                {children}
            </main>
            <PublicFooter />
        </div>
    );
}
