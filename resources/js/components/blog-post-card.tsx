import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { type BlogPost } from '@/types';
import { Link } from '@inertiajs/react';
import { Calendar, Clock, Eye, ArrowRight, User } from 'lucide-react';

interface BlogPostCardProps {
    post: BlogPost;
    className?: string;
    showAuthor?: boolean;
    showStats?: boolean;
}

export function BlogPostCard({ 
    post, 
    className = '', 
    showAuthor = true, 
    showStats = true 
}: BlogPostCardProps) {
    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Generate initials for author
    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    // Calculate reading time (rough estimate: 200 words per minute)
    const calculateReadingTime = (content: string) => {
        const wordsPerMinute = 200;
        const wordCount = content.split(/\s+/).length;
        const readingTime = Math.ceil(wordCount / wordsPerMinute);
        return readingTime;
    };

    const readingTime = post.reading_time || calculateReadingTime(post.content);

    return (
        <Card className={`group transition-all duration-300 hover:shadow-lg hover:-translate-y-1 overflow-hidden ${className}`}>
            {/* Featured Image */}
            {post.featured_image && (
                <div className="aspect-video overflow-hidden">
                    <img
                        src={post.featured_image}
                        alt={post.title}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                </div>
            )}

            <CardHeader className="pb-3">
                <div className="flex items-center justify-between mb-2">
                    {/* Category Badge */}
                    {post.category && (
                        <Badge variant="secondary" className="text-xs">
                            {post.category.name}
                        </Badge>
                    )}
                    
                    {/* Published Date */}
                    <div className="flex items-center text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(post.published_at || post.created_at)}
                    </div>
                </div>

                <CardTitle className="text-lg leading-tight line-clamp-2 group-hover:text-primary transition-colors">
                    <Link href={`/blog/${post.slug}`}>
                        {post.title}
                    </Link>
                </CardTitle>

                <CardDescription className="text-sm line-clamp-3 leading-relaxed">
                    {post.excerpt}
                </CardDescription>
            </CardHeader>

            <CardContent className="pb-3">
                {/* Author and Stats */}
                <div className="flex items-center justify-between">
                    {/* Author */}
                    {showAuthor && post.author && (
                        <div className="flex items-center space-x-2">
                            <Avatar className="h-6 w-6">
                                <AvatarImage 
                                    src={post.author.avatar} 
                                    alt={post.author.name}
                                />
                                <AvatarFallback className="text-xs bg-primary/10 text-primary">
                                    {getInitials(post.author.name)}
                                </AvatarFallback>
                            </Avatar>
                            <span className="text-xs text-muted-foreground">
                                {post.author.name}
                            </span>
                        </div>
                    )}

                    {/* Reading Stats */}
                    {showStats && (
                        <div className="flex items-center space-x-3 text-xs text-muted-foreground">
                            <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>{readingTime} min read</span>
                            </div>
                            {post.views_count > 0 && (
                                <div className="flex items-center space-x-1">
                                    <Eye className="h-3 w-3" />
                                    <span>{post.views_count}</span>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </CardContent>

            <CardFooter className="pt-3 border-t">
                <Button variant="ghost" className="w-full justify-between p-0 h-auto" asChild>
                    <Link href={`/blog/${post.slug}`}>
                        <span>Read More</span>
                        <ArrowRight className="h-4 w-4" />
                    </Link>
                </Button>
            </CardFooter>
        </Card>
    );
}

interface BlogPostGridProps {
    posts: BlogPost[];
    className?: string;
    showAuthor?: boolean;
    showStats?: boolean;
}

export function BlogPostGrid({ 
    posts, 
    className = '', 
    showAuthor = true, 
    showStats = true 
}: BlogPostGridProps) {
    // Filter only published posts
    const publishedPosts = posts.filter(post => post.status === 'published');

    return (
        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
            {publishedPosts.map((post) => (
                <BlogPostCard
                    key={post.id}
                    post={post}
                    showAuthor={showAuthor}
                    showStats={showStats}
                />
            ))}
        </div>
    );
}
