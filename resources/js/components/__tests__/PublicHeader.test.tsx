import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { PublicHeader } from '../public-header';

// Mock Inertia components and hooks
vi.mock('@inertiajs/react', () => ({
  Link: ({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
  usePage: () => ({
    props: {
      auth: {
        user: null,
      },
    },
  }),
}));

// Mock app logo components
vi.mock('../app-logo', () => ({
  default: () => <div data-testid="app-logo">App Logo</div>,
}));

vi.mock('../app-logo-icon', () => ({
  default: ({ className }: { className?: string }) => (
    <div data-testid="app-logo-icon" className={className}>
      Logo Icon
    </div>
  ),
}));

describe('PublicHeader Component', () => {
  it('renders the logo and brand name', () => {
    render(<PublicHeader />);
    
    expect(screen.getByTestId('app-logo-icon')).toBeInTheDocument();
    expect(screen.getByText('ConvertOKit')).toBeInTheDocument();
  });

  it('renders navigation menu items', () => {
    render(<PublicHeader />);
    
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Services')).toBeInTheDocument();
    expect(screen.getByText('Team')).toBeInTheDocument();
    expect(screen.getByText('Blog')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('renders contact information', () => {
    render(<PublicHeader />);
    
    expect(screen.getByText('+1 (234) 567-890')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('renders login and consultation buttons when user is not authenticated', () => {
    // Explicitly mock the usePage hook for this test
    vi.mocked(usePage).mockReturnValue({
      props: {
        auth: { user: null },
        flash: {},
      },
      url: '/',
      component: 'Home',
      version: '1',
      rememberedState: {},
      scrollRegions: [],
    });

    render(<PublicHeader />);

    // Should not show Dashboard link when user is not authenticated
    expect(screen.queryByRole('link', { name: /Dashboard/i })).not.toBeInTheDocument();

    // Should show Login link in desktop view (hidden on mobile)
    expect(screen.getByRole('link', { name: /Login/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Book Consultation/i })).toBeInTheDocument();
  });

  it('has correct navigation links', () => {
    render(<PublicHeader />);
    
    const homeLink = screen.getByRole('link', { name: 'Home' });
    const aboutLink = screen.getByRole('link', { name: 'About' });
    const servicesLink = screen.getByRole('link', { name: 'Services' });
    
    expect(homeLink).toHaveAttribute('href', '/');
    expect(aboutLink).toHaveAttribute('href', '/about');
    expect(servicesLink).toHaveAttribute('href', '/services');
  });

  it('has correct contact links', () => {
    render(<PublicHeader />);
    
    const phoneLink = screen.getByRole('link', { name: /\+1 \(234\) 567-890/ });
    const emailLink = screen.getByRole('link', { name: /hello@convertokit\.com/ });
    
    expect(phoneLink).toHaveAttribute('href', 'tel:+1234567890');
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
  });

  it('renders mobile menu trigger', () => {
    render(<PublicHeader />);
    
    const menuButton = screen.getByRole('button', { name: /Toggle menu/i });
    expect(menuButton).toBeInTheDocument();
  });

  it('has sticky positioning', () => {
    const { container } = render(<PublicHeader />);
    
    const header = container.querySelector('header');
    expect(header).toHaveClass('sticky', 'top-0', 'z-50');
  });

  it('has backdrop blur effect', () => {
    const { container } = render(<PublicHeader />);
    
    const header = container.querySelector('header');
    expect(header).toHaveClass('backdrop-blur');
  });
});

describe('PublicHeader with authenticated user', () => {
  it('renders dashboard button when user is authenticated', () => {
    // Mock authenticated user
    vi.mock('@inertiajs/react', () => ({
      Link: ({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) => (
        <a href={href} {...props}>
          {children}
        </a>
      ),
      usePage: () => ({
        props: {
          auth: {
            user: {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
            },
          },
        },
      }),
    }));

    render(<PublicHeader />);
    
    expect(screen.getByRole('link', { name: /Dashboard/i })).toBeInTheDocument();
  });
});
