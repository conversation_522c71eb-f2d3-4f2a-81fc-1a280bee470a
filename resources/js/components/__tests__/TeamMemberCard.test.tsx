import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { TeamMemberCard, TeamGrid } from '../team-member-card';
import type { TeamMember } from '@/types';

// Mock Inertia Link component
vi.mock('@inertiajs/react', () => ({
  Link: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

const mockTeamMember: TeamMember = {
  id: 1,
  user_id: 1,
  name: '<PERSON>',
  position: 'Meta Ads Specialist',
  bio: 'Expert in Facebook and Instagram advertising with 5+ years of experience.',
  avatar: '/images/john-doe.jpg',
  expertise: ['Facebook Ads', 'Instagram Ads', 'Conversion Optimization'],
  is_active: true,
  sort_order: 1,
  created_at: '2025-01-01T00:00:00.000Z',
  updated_at: '2025-01-01T00:00:00.000Z',
  user: {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'team_member',
    email_verified_at: '2025-01-01T00:00:00.000Z',
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  },
};

describe('TeamMemberCard Component', () => {
  it('renders team member information correctly', () => {
    render(<TeamMemberCard member={mockTeamMember} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Meta Ads Specialist')).toBeInTheDocument();
    expect(screen.getByText('Expert in Facebook and Instagram advertising with 5+ years of experience.')).toBeInTheDocument();
  });

  it('renders expertise tags', () => {
    render(<TeamMemberCard member={mockTeamMember} />);
    
    expect(screen.getByText('Facebook Ads')).toBeInTheDocument();
    expect(screen.getByText('Instagram Ads')).toBeInTheDocument();
    expect(screen.getByText('Conversion Optimization')).toBeInTheDocument();
    expect(screen.getByText('Expertise:')).toBeInTheDocument();
  });

  it('renders contact actions when showContact is true', () => {
    render(<TeamMemberCard member={mockTeamMember} showContact={true} />);
    
    expect(screen.getByRole('link', { name: /Email/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /LinkedIn/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Book Consultation/i })).toBeInTheDocument();
  });

  it('does not render contact actions when showContact is false', () => {
    render(<TeamMemberCard member={mockTeamMember} showContact={false} />);
    
    expect(screen.queryByRole('link', { name: /Email/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('link', { name: /LinkedIn/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('link', { name: /Book Consultation/i })).not.toBeInTheDocument();
  });

  it('renders avatar with correct alt text', () => {
    render(<TeamMemberCard member={mockTeamMember} />);
    
    const avatar = screen.getByRole('img', { name: 'John Doe' });
    expect(avatar).toBeInTheDocument();
  });

  it('renders initials fallback when no avatar', () => {
    const memberWithoutAvatar = {
      ...mockTeamMember,
      avatar: undefined,
    };

    render(<TeamMemberCard member={memberWithoutAvatar} />);
    
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('handles member without bio', () => {
    const memberWithoutBio = {
      ...mockTeamMember,
      bio: undefined,
    };

    render(<TeamMemberCard member={memberWithoutBio} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Meta Ads Specialist')).toBeInTheDocument();
  });

  it('handles member without expertise', () => {
    const memberWithoutExpertise = {
      ...mockTeamMember,
      expertise: [],
    };

    render(<TeamMemberCard member={memberWithoutExpertise} />);
    
    expect(screen.queryByText('Expertise:')).not.toBeInTheDocument();
  });

  it('has correct email link', () => {
    render(<TeamMemberCard member={mockTeamMember} />);
    
    const emailLink = screen.getByRole('link', { name: /Email/i });
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
  });

  it('applies custom className', () => {
    const { container } = render(<TeamMemberCard member={mockTeamMember} className="custom-member-class" />);
    
    expect(container.firstChild).toHaveClass('custom-member-class');
  });
});

describe('TeamGrid Component', () => {
  const mockTeamMembers: TeamMember[] = [
    mockTeamMember,
    {
      ...mockTeamMember,
      id: 2,
      name: 'Jane Smith',
      position: 'Analytics Expert',
      is_active: true,
      sort_order: 2,
    },
    {
      ...mockTeamMember,
      id: 3,
      name: 'Bob Johnson',
      position: 'Inactive Member',
      is_active: false,
      sort_order: 3,
    },
  ];

  it('renders active team members only', () => {
    render(<TeamGrid members={mockTeamMembers} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.queryByText('Bob Johnson')).not.toBeInTheDocument();
  });

  it('sorts members by sort_order', () => {
    const unsortedMembers = [
      { ...mockTeamMember, id: 2, name: 'Second Member', sort_order: 2 },
      { ...mockTeamMember, id: 1, name: 'First Member', sort_order: 1 },
      { ...mockTeamMember, id: 3, name: 'Third Member', sort_order: 3 },
    ];

    render(<TeamGrid members={unsortedMembers} />);
    
    const memberCards = screen.getAllByText(/Member$/);
    expect(memberCards[0]).toHaveTextContent('First Member');
    expect(memberCards[1]).toHaveTextContent('Second Member');
    expect(memberCards[2]).toHaveTextContent('Third Member');
  });

  it('applies custom className', () => {
    const { container } = render(<TeamGrid members={mockTeamMembers} className="custom-grid-class" />);
    
    expect(container.firstChild).toHaveClass('custom-grid-class');
  });

  it('passes showContact prop to individual cards', () => {
    render(<TeamGrid members={[mockTeamMember]} showContact={false} />);
    
    expect(screen.queryByRole('link', { name: /Email/i })).not.toBeInTheDocument();
  });

  it('handles empty members array', () => {
    const { container } = render(<TeamGrid members={[]} />);
    
    expect(container.firstChild?.children).toHaveLength(0);
  });
});
