import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { LoadingSpinner, PageLoading, InlineLoading } from '../loading-spinner';

describe('LoadingSpinner Component', () => {
  it('renders with default props', () => {
    render(<LoadingSpinner />);
    
    const spinner = screen.getByRole('generic');
    expect(spinner).toBeInTheDocument();
  });

  it('renders with custom text', () => {
    render(<LoadingSpinner text="Custom loading text" />);
    
    expect(screen.getByText('Custom loading text')).toBeInTheDocument();
  });

  it('applies small size class', () => {
    const { container } = render(<LoadingSpinner size="sm" />);
    
    const spinnerIcon = container.querySelector('svg');
    expect(spinnerIcon).toHaveClass('h-4', 'w-4');
  });

  it('applies medium size class (default)', () => {
    const { container } = render(<LoadingSpinner size="md" />);
    
    const spinnerIcon = container.querySelector('svg');
    expect(spinnerIcon).toHaveClass('h-6', 'w-6');
  });

  it('applies large size class', () => {
    const { container } = render(<LoadingSpinner size="lg" />);
    
    const spinnerIcon = container.querySelector('svg');
    expect(spinnerIcon).toHaveClass('h-8', 'w-8');
  });

  it('applies custom className', () => {
    const { container } = render(<LoadingSpinner className="custom-spinner-class" />);
    
    expect(container.firstChild).toHaveClass('custom-spinner-class');
  });

  it('has spinning animation', () => {
    const { container } = render(<LoadingSpinner />);
    
    const spinnerIcon = container.querySelector('svg');
    expect(spinnerIcon).toHaveClass('animate-spin');
  });

  it('renders without text when not provided', () => {
    render(<LoadingSpinner />);
    
    expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
  });
});

describe('PageLoading Component', () => {
  it('renders with default text', () => {
    render(<PageLoading />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders with custom text', () => {
    render(<PageLoading text="Please wait..." />);
    
    expect(screen.getByText('Please wait...')).toBeInTheDocument();
  });

  it('has minimum height for page loading', () => {
    const { container } = render(<PageLoading />);
    
    expect(container.firstChild).toHaveClass('min-h-[400px]');
  });

  it('centers content', () => {
    const { container } = render(<PageLoading />);
    
    expect(container.firstChild).toHaveClass('flex', 'items-center', 'justify-center');
  });

  it('applies custom className', () => {
    const { container } = render(<PageLoading className="custom-page-loading" />);
    
    expect(container.firstChild).toHaveClass('custom-page-loading');
  });

  it('uses large spinner size', () => {
    const { container } = render(<PageLoading />);
    
    const spinnerIcon = container.querySelector('svg');
    expect(spinnerIcon).toHaveClass('h-8', 'w-8');
  });
});

describe('InlineLoading Component', () => {
  it('renders with default text', () => {
    render(<InlineLoading />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders with custom text', () => {
    render(<InlineLoading text="Saving..." />);
    
    expect(screen.getByText('Saving...')).toBeInTheDocument();
  });

  it('displays horizontally', () => {
    const { container } = render(<InlineLoading />);
    
    expect(container.firstChild).toHaveClass('flex', 'items-center', 'space-x-2');
  });

  it('applies custom className', () => {
    const { container } = render(<InlineLoading className="custom-inline-loading" />);
    
    expect(container.firstChild).toHaveClass('custom-inline-loading');
  });

  it('uses small spinner size', () => {
    const { container } = render(<InlineLoading />);
    
    const spinnerIcon = container.querySelector('svg');
    expect(spinnerIcon).toHaveClass('h-4', 'w-4');
  });

  it('has proper text styling', () => {
    render(<InlineLoading />);
    
    const text = screen.getByText('Loading...');
    expect(text).toHaveClass('text-sm', 'text-muted-foreground');
  });
});
