import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { ErrorBoundary, ErrorPage, NotFoundPage } from '../error-boundary';

// Mock Inertia Link component
vi.mock('@inertiajs/react', () => ({
  Link: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('ErrorBoundary Component', () => {
  // Suppress console.error for these tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = vi.fn();
  });
  afterAll(() => {
    console.error = originalError;
  });

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('renders error UI when there is an error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText(/We're sorry, but something unexpected happened/)).toBeInTheDocument();
  });

  it('renders custom fallback when provided', () => {
    const customFallback = <div>Custom error fallback</div>;
    
    render(
      <ErrorBoundary fallback={customFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Custom error fallback')).toBeInTheDocument();
  });

  it('renders Try Again and Go Home buttons', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByRole('button', { name: /Try Again/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Go Home/i })).toBeInTheDocument();
  });

  it('has correct link href for Go Home button', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    const homeLink = screen.getByRole('link', { name: /Go Home/i });
    expect(homeLink).toHaveAttribute('href', '/');
  });

  it('shows error details in development mode', () => {
    // Mock development environment
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Test error')).toBeInTheDocument();
    
    // Restore environment
    process.env.NODE_ENV = originalEnv;
  });
});

describe('ErrorPage Component', () => {
  it('renders with default props', () => {
    render(<ErrorPage />);
    
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText(/We're sorry, but something unexpected happened/)).toBeInTheDocument();
  });

  it('renders with custom title and description', () => {
    render(
      <ErrorPage 
        title="Custom Error Title" 
        description="Custom error description" 
      />
    );
    
    expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
    expect(screen.getByText('Custom error description')).toBeInTheDocument();
  });

  it('shows error details when provided in development', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';
    
    render(<ErrorPage error="Custom error message" />);
    
    expect(screen.getByText('Custom error message')).toBeInTheDocument();
    
    process.env.NODE_ENV = originalEnv;
  });

  it('renders Try Again button when showRetry is true and onRetry is provided', () => {
    const mockRetry = vi.fn();
    
    render(<ErrorPage showRetry={true} onRetry={mockRetry} />);
    
    expect(screen.getByRole('button', { name: /Try Again/i })).toBeInTheDocument();
  });

  it('does not render Try Again button when showRetry is false', () => {
    render(<ErrorPage showRetry={false} />);
    
    expect(screen.queryByRole('button', { name: /Try Again/i })).not.toBeInTheDocument();
  });

  it('renders Go Home button', () => {
    render(<ErrorPage />);
    
    const homeLink = screen.getByRole('link', { name: /Go Home/i });
    expect(homeLink).toBeInTheDocument();
    expect(homeLink).toHaveAttribute('href', '/');
  });
});

describe('NotFoundPage Component', () => {
  it('renders with default props', () => {
    render(<NotFoundPage />);
    
    expect(screen.getByText('404')).toBeInTheDocument();
    expect(screen.getByText('Page not found')).toBeInTheDocument();
    expect(screen.getByText(/The page you're looking for doesn't exist/)).toBeInTheDocument();
  });

  it('renders with custom title and description', () => {
    render(
      <NotFoundPage 
        title="Custom 404 Title" 
        description="Custom 404 description" 
      />
    );
    
    expect(screen.getByText('Custom 404 Title')).toBeInTheDocument();
    expect(screen.getByText('Custom 404 description')).toBeInTheDocument();
  });

  it('renders Back to Home button', () => {
    render(<NotFoundPage />);
    
    const homeLink = screen.getByRole('link', { name: /Back to Home/i });
    expect(homeLink).toBeInTheDocument();
    expect(homeLink).toHaveAttribute('href', '/');
  });

  it('displays 404 number prominently', () => {
    render(<NotFoundPage />);
    
    const notFoundNumber = screen.getByText('404');
    expect(notFoundNumber).toHaveClass('text-6xl', 'font-bold');
  });
});
