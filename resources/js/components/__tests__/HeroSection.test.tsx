import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { HeroSection } from '../hero-section';

// Mock Inertia Link component
vi.mock('@inertiajs/react', () => ({
  Link: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

describe('HeroSection Component', () => {
  it('renders with default props', () => {
    render(<HeroSection />);
    
    expect(screen.getByText('Maximize Your Meta Ads ROI with Expert Analytics')).toBeInTheDocument();
    expect(screen.getByText('ConvertOKit')).toBeInTheDocument();
    expect(screen.getByText(/Transform your digital marketing/)).toBeInTheDocument();
  });

  it('renders custom title and description', () => {
    const customProps = {
      title: 'Custom Hero Title',
      subtitle: 'Custom Subtitle',
      description: 'Custom description text',
    };

    render(<HeroSection {...customProps} />);
    
    expect(screen.getByText('Custom Hero Title')).toBeInTheDocument();
    expect(screen.getByText('Custom Subtitle')).toBeInTheDocument();
    expect(screen.getByText('Custom description text')).toBeInTheDocument();
  });

  it('renders primary and secondary CTAs', () => {
    render(<HeroSection />);
    
    expect(screen.getByRole('link', { name: /Book Free Consultation/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /View Our Services/i })).toBeInTheDocument();
  });

  it('renders custom CTAs when provided', () => {
    const customProps = {
      primaryCta: { text: 'Custom Primary', href: '/custom-primary' },
      secondaryCta: { text: 'Custom Secondary', href: '/custom-secondary' },
    };

    render(<HeroSection {...customProps} />);
    
    expect(screen.getByRole('link', { name: /Custom Primary/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Custom Secondary/i })).toBeInTheDocument();
  });

  it('renders stats when provided', () => {
    const customStats = [
      { value: '100%', label: 'Success Rate' },
      { value: '50+', label: 'Happy Clients' },
    ];

    render(<HeroSection stats={customStats} />);
    
    expect(screen.getByText('100%')).toBeInTheDocument();
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
    expect(screen.getByText('50+')).toBeInTheDocument();
    expect(screen.getByText('Happy Clients')).toBeInTheDocument();
  });

  it('renders badge when provided', () => {
    render(<HeroSection badge="🎉 Special Offer" />);
    
    expect(screen.getByText('🎉 Special Offer')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<HeroSection className="custom-hero-class" />);
    
    expect(container.firstChild).toHaveClass('custom-hero-class');
  });

  it('renders trust indicators', () => {
    render(<HeroSection />);
    
    expect(screen.getByText('4.9/5 Client Rating')).toBeInTheDocument();
    expect(screen.getByText('Trusted by leading brands worldwide')).toBeInTheDocument();
  });

  it('has correct link hrefs', () => {
    render(<HeroSection />);
    
    const primaryLink = screen.getByRole('link', { name: /Book Free Consultation/i });
    const secondaryLink = screen.getByRole('link', { name: /View Our Services/i });
    
    expect(primaryLink).toHaveAttribute('href', '/book-consultation');
    expect(secondaryLink).toHaveAttribute('href', '/services');
  });
});
