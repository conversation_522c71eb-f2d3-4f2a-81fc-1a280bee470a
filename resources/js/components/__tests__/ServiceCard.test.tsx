import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { ServiceCard, ServiceGrid } from '../service-card';
import type { Service } from '@/types';

// Mock Inertia Link component
vi.mock('@inertiajs/react', () => ({
  Link: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

const mockService: Service = {
  id: 1,
  title: 'Meta Ads Management',
  slug: 'meta-ads-management',
  description: 'Complete Facebook and Instagram advertising management',
  detailed_description: 'Detailed description here',
  features: ['Campaign Setup', 'Performance Optimization', 'Monthly Reporting'],
  price_range: '$2,000 - $5,000',
  category: 'Meta Advertising',
  is_active: true,
  sort_order: 1,
  meta_title: 'Meta Ads Management',
  meta_description: 'Meta description',
  created_at: '2025-01-01T00:00:00.000Z',
  updated_at: '2025-01-01T00:00:00.000Z',
};

describe('ServiceCard Component', () => {
  it('renders service information correctly', () => {
    render(<ServiceCard service={mockService} />);
    
    expect(screen.getByText('Meta Ads Management')).toBeInTheDocument();
    expect(screen.getByText('Complete Facebook and Instagram advertising management')).toBeInTheDocument();
    expect(screen.getByText('$2,000 - $5,000')).toBeInTheDocument();
    expect(screen.getByText('Meta Advertising')).toBeInTheDocument();
  });

  it('renders features list', () => {
    render(<ServiceCard service={mockService} />);
    
    expect(screen.getByText('Campaign Setup')).toBeInTheDocument();
    expect(screen.getByText('Performance Optimization')).toBeInTheDocument();
    expect(screen.getByText('Monthly Reporting')).toBeInTheDocument();
    expect(screen.getByText("What's included:")).toBeInTheDocument();
  });

  it('shows featured badge when featured prop is true', () => {
    render(<ServiceCard service={mockService} featured={true} />);
    
    expect(screen.getByText('Most Popular')).toBeInTheDocument();
  });

  it('does not show featured badge when featured prop is false', () => {
    render(<ServiceCard service={mockService} featured={false} />);
    
    expect(screen.queryByText('Most Popular')).not.toBeInTheDocument();
  });

  it('renders action buttons', () => {
    render(<ServiceCard service={mockService} />);
    
    expect(screen.getByRole('link', { name: /Learn More/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Book Now/i })).toBeInTheDocument();
  });

  it('has correct link hrefs', () => {
    render(<ServiceCard service={mockService} />);
    
    const learnMoreLink = screen.getByRole('link', { name: /Learn More/i });
    const bookNowLink = screen.getByRole('link', { name: /Book Now/i });
    
    expect(learnMoreLink).toHaveAttribute('href', '/services/meta-ads-management');
    expect(bookNowLink).toHaveAttribute('href', '/book-consultation');
  });

  it('handles service with many features', () => {
    const serviceWithManyFeatures = {
      ...mockService,
      features: ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4', 'Feature 5', 'Feature 6'],
    };

    render(<ServiceCard service={serviceWithManyFeatures} />);
    
    expect(screen.getByText('Feature 1')).toBeInTheDocument();
    expect(screen.getByText('Feature 4')).toBeInTheDocument();
    expect(screen.getByText('+2 more features')).toBeInTheDocument();
  });

  it('handles service without features', () => {
    const serviceWithoutFeatures = {
      ...mockService,
      features: [],
    };

    render(<ServiceCard service={serviceWithoutFeatures} />);
    
    expect(screen.queryByText("What's included:")).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<ServiceCard service={mockService} className="custom-service-class" />);
    
    expect(container.firstChild).toHaveClass('custom-service-class');
  });
});

describe('ServiceGrid Component', () => {
  const mockServices: Service[] = [
    mockService,
    {
      ...mockService,
      id: 2,
      title: 'Facebook Pixel Setup',
      slug: 'facebook-pixel-setup',
    },
  ];

  it('renders multiple services', () => {
    render(<ServiceGrid services={mockServices} />);
    
    expect(screen.getByText('Meta Ads Management')).toBeInTheDocument();
    expect(screen.getByText('Facebook Pixel Setup')).toBeInTheDocument();
  });

  it('applies featured styling to specified service', () => {
    render(<ServiceGrid services={mockServices} featuredServiceId={1} />);
    
    expect(screen.getByText('Most Popular')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<ServiceGrid services={mockServices} className="custom-grid-class" />);
    
    expect(container.firstChild).toHaveClass('custom-grid-class');
  });

  it('handles empty services array', () => {
    const { container } = render(<ServiceGrid services={[]} />);
    
    expect(container.firstChild?.children).toHaveLength(0);
  });
});
