import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import useDebounce from '../use-debounce';

describe('useDebounce Hook', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('returns initial value immediately', () => {
    const { result } = renderHook(() => useDebounce('initial', 500));
    
    expect(result.current).toBe('initial');
  });

  it('debounces value changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 },
      }
    );
    
    expect(result.current).toBe('initial');
    
    // Change the value
    rerender({ value: 'updated', delay: 500 });
    
    // Value should not change immediately
    expect(result.current).toBe('initial');
    
    // Fast-forward time by 500ms
    act(() => {
      vi.advanceTimersByTime(500);
    });
    
    // Now the value should be updated
    expect(result.current).toBe('updated');
  });

  it('cancels previous timeout when value changes quickly', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 },
      }
    );
    
    // Change value multiple times quickly
    rerender({ value: 'first', delay: 500 });
    
    act(() => {
      vi.advanceTimersByTime(200);
    });
    
    rerender({ value: 'second', delay: 500 });
    
    act(() => {
      vi.advanceTimersByTime(200);
    });
    
    rerender({ value: 'final', delay: 500 });
    
    // Value should still be initial
    expect(result.current).toBe('initial');
    
    // Complete the debounce period
    act(() => {
      vi.advanceTimersByTime(500);
    });
    
    // Should have the final value, not intermediate ones
    expect(result.current).toBe('final');
  });

  it('handles delay changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 },
      }
    );
    
    rerender({ value: 'updated', delay: 1000 });
    
    // Advance by 500ms (original delay)
    act(() => {
      vi.advanceTimersByTime(500);
    });
    
    // Should not be updated yet because delay changed to 1000ms
    expect(result.current).toBe('initial');
    
    // Advance by another 500ms (total 1000ms)
    act(() => {
      vi.advanceTimersByTime(500);
    });
    
    // Now should be updated
    expect(result.current).toBe('updated');
  });

  it('works with different data types', () => {
    // Test with numbers
    const { result: numberResult, rerender: numberRerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 0, delay: 100 },
      }
    );
    
    numberRerender({ value: 42, delay: 100 });
    
    act(() => {
      vi.advanceTimersByTime(100);
    });
    
    expect(numberResult.current).toBe(42);
    
    // Test with objects
    const { result: objectResult, rerender: objectRerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: { name: 'initial' }, delay: 100 },
      }
    );
    
    const newObject = { name: 'updated' };
    objectRerender({ value: newObject, delay: 100 });
    
    act(() => {
      vi.advanceTimersByTime(100);
    });
    
    expect(objectResult.current).toBe(newObject);
  });

  it('cleans up timeout on unmount', () => {
    const { result, rerender, unmount } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 },
      }
    );
    
    rerender({ value: 'updated', delay: 500 });
    
    // Unmount before timeout completes
    unmount();
    
    // Advance time
    act(() => {
      vi.advanceTimersByTime(500);
    });
    
    // Should not cause any issues (no memory leaks)
    expect(true).toBe(true); // Test passes if no errors thrown
  });

  it('handles zero delay', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 0 },
      }
    );
    
    rerender({ value: 'updated', delay: 0 });
    
    // With zero delay, should update immediately after next tick
    act(() => {
      vi.advanceTimersByTime(0);
    });
    
    expect(result.current).toBe('updated');
  });

  it('handles negative delay', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: -100 },
      }
    );
    
    rerender({ value: 'updated', delay: -100 });
    
    // Negative delay should be treated as 0
    act(() => {
      vi.advanceTimersByTime(0);
    });
    
    expect(result.current).toBe('updated');
  });
});
