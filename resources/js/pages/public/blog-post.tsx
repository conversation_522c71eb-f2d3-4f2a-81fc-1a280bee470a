import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import PublicLayout from '@/layouts/public-layout';
import { type BlogPost, type User, type Category } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowRight, Calendar, Clock, Eye, User as UserIcon, Tag } from 'lucide-react';
import { formatDate, getInitials } from '@/lib/formatters';

interface BlogPostProps {
    slug: string;
    post?: BlogPost;
    relatedPosts?: BlogPost[];
}

export default function BlogPostDetail({ slug, post, relatedPosts = [] }: BlogPostProps) {
    // Mock blog post data for demonstration - this will be replaced with real data from backend
    const mockPost: BlogPost = {
        id: 1,
        title: 'The Ultimate Guide to Meta Ads Optimization in 2025',
        slug: 'meta-ads-optimization-guide-2025',
        excerpt: 'Learn the latest strategies and techniques to optimize your Facebook and Instagram advertising campaigns for maximum ROI.',
        content: `
            <p>Meta advertising continues to evolve, and staying ahead of the curve is crucial for maximizing your return on investment. In this comprehensive guide, we'll explore the latest strategies and techniques for optimizing your Facebook and Instagram advertising campaigns in 2025.</p>
            
            <h2>Understanding the Meta Algorithm Changes</h2>
            <p>The Meta algorithm has undergone significant changes in recent months, affecting how ads are delivered and optimized. Understanding these changes is crucial for campaign success.</p>
            
            <h3>Key Algorithm Updates</h3>
            <ul>
                <li>Enhanced machine learning capabilities</li>
                <li>Improved audience targeting precision</li>
                <li>Better cross-platform optimization</li>
                <li>Advanced creative testing features</li>
            </ul>
            
            <h2>Advanced Targeting Strategies</h2>
            <p>Effective targeting is the foundation of successful Meta advertising. Here are the latest strategies that are driving results:</p>
            
            <h3>1. Lookalike Audiences 2.0</h3>
            <p>The new lookalike audience features allow for more precise targeting based on customer lifetime value and engagement patterns.</p>
            
            <h3>2. Interest Stacking</h3>
            <p>Combining multiple interests and behaviors to create highly targeted audience segments that convert better than broad targeting.</p>
            
            <h2>Creative Optimization Techniques</h2>
            <p>Your ad creative is what captures attention and drives action. Here's how to optimize your creatives for better performance:</p>
            
            <ul>
                <li>Use dynamic creative testing to find winning combinations</li>
                <li>Implement video-first strategies for higher engagement</li>
                <li>Leverage user-generated content for authenticity</li>
                <li>Test different ad formats and placements</li>
            </ul>
            
            <h2>Conversion Tracking and Attribution</h2>
            <p>Accurate tracking is essential for optimization. With iOS 14.5+ changes, tracking has become more complex but not impossible.</p>
            
            <h3>Best Practices for Tracking</h3>
            <ul>
                <li>Implement Conversions API for server-side tracking</li>
                <li>Use multiple attribution windows</li>
                <li>Set up proper UTM parameters</li>
                <li>Leverage first-party data collection</li>
            </ul>
            
            <h2>Budget Optimization Strategies</h2>
            <p>Making the most of your advertising budget requires strategic planning and continuous optimization.</p>
            
            <p>Campaign Budget Optimization (CBO) should be used strategically, not as a default setting. Test both CBO and ad set-level budgets to find what works best for your campaigns.</p>
            
            <h2>Conclusion</h2>
            <p>Meta advertising success in 2025 requires a combination of strategic thinking, technical expertise, and continuous testing. By implementing these optimization techniques, you'll be well-positioned to achieve better results from your advertising investments.</p>
            
            <p>Remember, optimization is an ongoing process. What works today may not work tomorrow, so stay flexible and keep testing new approaches.</p>
        `,
        featured_image: '/images/blog/meta-ads-optimization.jpg',
        status: 'published',
        published_at: '2025-01-15T10:00:00.000Z',
        reading_time: 8,
        views_count: 1250,
        category_id: 1,
        author_id: 1,
        meta_title: 'The Ultimate Guide to Meta Ads Optimization in 2025 - ConvertOKit',
        meta_description: 'Learn the latest strategies and techniques to optimize your Facebook and Instagram advertising campaigns for maximum ROI in 2025.',
        created_at: '2025-01-15T10:00:00.000Z',
        updated_at: '2025-01-15T10:00:00.000Z',
        category: {
            id: 1,
            name: 'Meta Advertising',
            slug: 'meta-advertising',
            description: 'Tips and strategies for Facebook and Instagram advertising',
            color: '#1877F2',
            is_active: true,
            sort_order: 1,
            created_at: '2025-01-01T00:00:00.000Z',
            updated_at: '2025-01-01T00:00:00.000Z',
        },
        author: {
            id: 1,
            name: 'John Smith',
            email: '<EMAIL>',
            role: 'team_member',
            email_verified_at: '2025-01-01T00:00:00.000Z',
            created_at: '2025-01-01T00:00:00.000Z',
            updated_at: '2025-01-01T00:00:00.000Z',
        }
    };

    const displayPost = post || mockPost;

    return (
        <PublicLayout>
            <Head 
                title={displayPost.meta_title || `${displayPost.title} - ConvertOKit Blog`}
                description={displayPost.meta_description || displayPost.excerpt}
            />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-8">
                            {displayPost.category && (
                                <Badge variant="secondary" className="mb-4">
                                    <Tag className="h-3 w-3 mr-1" />
                                    {displayPost.category.name}
                                </Badge>
                            )}
                            <h1 className="text-4xl font-bold mb-6 sm:text-5xl leading-tight">
                                {displayPost.title}
                            </h1>
                            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                                {displayPost.excerpt}
                            </p>
                            
                            {/* Article Meta */}
                            <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-muted-foreground">
                                {displayPost.author && (
                                    <div className="flex items-center space-x-2">
                                        <Avatar className="h-8 w-8">
                                            <AvatarImage src={`/images/team/${displayPost.author.name.toLowerCase().replace(' ', '-')}.jpg`} />
                                            <AvatarFallback>
                                                {getInitials(displayPost.author.name)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <span>By {displayPost.author.name}</span>
                                    </div>
                                )}
                                <div className="flex items-center space-x-1">
                                    <Calendar className="h-4 w-4" />
                                    <span>{formatDate(displayPost.published_at)}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                    <Clock className="h-4 w-4" />
                                    <span>{displayPost.reading_time} min read</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                    <Eye className="h-4 w-4" />
                                    <span>{displayPost.views_count.toLocaleString()} views</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Featured Image */}
            {displayPost.featured_image && (
                <section className="py-8">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto">
                            <img 
                                src={displayPost.featured_image} 
                                alt={displayPost.title}
                                className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                            />
                        </div>
                    </div>
                </section>
            )}

            {/* Article Content */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div 
                            className="prose prose-lg max-w-none prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground prose-ul:text-muted-foreground prose-ol:text-muted-foreground"
                            dangerouslySetInnerHTML={{ __html: displayPost.content }}
                        />
                    </div>
                </div>
            </section>

            {/* Author Bio */}
            {displayPost.author && (
                <section className="py-16 bg-muted/30">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto">
                            <Card className="border-0 shadow-md">
                                <CardContent className="p-8">
                                    <div className="flex items-start space-x-4">
                                        <Avatar className="h-16 w-16">
                                            <AvatarImage src={`/images/team/${displayPost.author.name.toLowerCase().replace(' ', '-')}.jpg`} />
                                            <AvatarFallback className="text-lg">
                                                {getInitials(displayPost.author.name)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1">
                                            <h3 className="text-xl font-semibold mb-2">About {displayPost.author.name}</h3>
                                            <p className="text-muted-foreground mb-4">
                                                {displayPost.author.name} is a digital marketing specialist at ConvertOKit, 
                                                focusing on Meta advertising and conversion optimization. With years of experience 
                                                in the industry, they help businesses maximize their advertising ROI through 
                                                strategic campaign management and advanced tracking implementation.
                                            </p>
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href="/team">
                                                    View Profile
                                                    <ArrowRight className="ml-2 h-4 w-4" />
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>
            )}

            {/* Related Posts */}
            {relatedPosts.length > 0 && (
                <section className="py-16">
                    <div className="container mx-auto px-4">
                        <div className="max-w-6xl mx-auto">
                            <h2 className="text-3xl font-bold text-center mb-12">Related Articles</h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {relatedPosts.slice(0, 3).map((relatedPost) => (
                                    <Card key={relatedPost.id} className="border-0 shadow-md hover:shadow-lg transition-shadow">
                                        <CardHeader>
                                            {relatedPost.category && (
                                                <Badge variant="secondary" className="w-fit mb-2">
                                                    {relatedPost.category.name}
                                                </Badge>
                                            )}
                                            <CardTitle className="text-lg line-clamp-2">
                                                <Link href={`/blog/${relatedPost.slug}`} className="hover:text-primary">
                                                    {relatedPost.title}
                                                </Link>
                                            </CardTitle>
                                            <CardDescription className="line-clamp-3">
                                                {relatedPost.excerpt}
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                                                <span>{formatDate(relatedPost.published_at)}</span>
                                                <span>{relatedPost.reading_time} min read</span>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>
            )}

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Need Help With Your Meta Advertising?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let our experts help you implement these strategies and optimize your campaigns for better results
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/blog">
                                Read More Articles
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
