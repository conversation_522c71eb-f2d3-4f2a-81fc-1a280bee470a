import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Home from '../public/home';

// Mock all the components used in Home
vi.mock('@/components/hero-section', () => ({
  HeroSection: () => <div data-testid="hero-section">Hero Section</div>,
}));

vi.mock('@/components/service-card', () => ({
  ServiceGrid: ({ services }: any) => (
    <div data-testid="service-grid">
      {services.map((service: any) => (
        <div key={service.id} data-testid={`service-${service.id}`}>
          {service.title}
        </div>
      ))}
    </div>
  ),
}));

vi.mock('@/components/team-member-card', () => ({
  TeamGrid: ({ members }: any) => (
    <div data-testid="team-grid">
      {members.map((member: any) => (
        <div key={member.id} data-testid={`member-${member.id}`}>
          {member.name}
        </div>
      ))}
    </div>
  ),
}));

vi.mock('@/components/blog-post-card', () => ({
  BlogPostGrid: ({ posts }: any) => (
    <div data-testid="blog-grid">
      {posts.map((post: any) => (
        <div key={post.id} data-testid={`post-${post.id}`}>
          {post.title}
        </div>
      ))}
    </div>
  ),
}));

vi.mock('@/layouts/public-layout', () => ({
  default: ({ children }: any) => <div data-testid="public-layout">{children}</div>,
}));

vi.mock('@inertiajs/react', () => ({
  Head: ({ title }: any) => <title>{title}</title>,
  Link: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

describe('Home Page', () => {
  it('renders without crashing', () => {
    render(<Home />);
    
    expect(screen.getByTestId('public-layout')).toBeInTheDocument();
    expect(screen.getByTestId('hero-section')).toBeInTheDocument();
  });

  it('renders hero section', () => {
    render(<Home />);
    
    expect(screen.getByTestId('hero-section')).toBeInTheDocument();
  });

  it('renders about section', () => {
    render(<Home />);
    
    expect(screen.getByText('About ConvertOKit')).toBeInTheDocument();
    expect(screen.getByText(/We're a team of digital marketing specialists/)).toBeInTheDocument();
  });

  it('renders services section with mock services', () => {
    render(<Home />);
    
    expect(screen.getByText('Our Services')).toBeInTheDocument();
    expect(screen.getByTestId('service-grid')).toBeInTheDocument();
    
    // Check for mock services
    expect(screen.getByTestId('service-1')).toBeInTheDocument();
    expect(screen.getByText('Meta Ads Management')).toBeInTheDocument();
  });

  it('renders services section with provided services', () => {
    const mockServices = [
      {
        id: 1,
        title: 'Custom Service',
        slug: 'custom-service',
        description: 'Custom description',
        detailed_description: '',
        features: [],
        price_range: '$1000',
        category: 'Custom',
        is_active: true,
        sort_order: 1,
        meta_title: '',
        meta_description: '',
        created_at: '',
        updated_at: '',
      },
    ];

    render(<Home services={mockServices} />);
    
    expect(screen.getByText('Custom Service')).toBeInTheDocument();
  });

  it('renders team section when team members are provided', () => {
    const mockTeamMembers = [
      {
        id: 1,
        user_id: 1,
        name: 'John Doe',
        position: 'Developer',
        bio: 'Bio',
        avatar: '',
        expertise: [],
        is_active: true,
        sort_order: 1,
        created_at: '',
        updated_at: '',
        user: {
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'team_member',
          email_verified_at: '',
          created_at: '',
          updated_at: '',
        },
      },
    ];

    render(<Home teamMembers={mockTeamMembers} />);
    
    expect(screen.getByText('Meet Our Team')).toBeInTheDocument();
    expect(screen.getByTestId('team-grid')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('does not render team section when no team members', () => {
    render(<Home teamMembers={[]} />);
    
    expect(screen.queryByText('Meet Our Team')).not.toBeInTheDocument();
  });

  it('renders blog section when blog posts are provided', () => {
    const mockBlogPosts = [
      {
        id: 1,
        title: 'Test Blog Post',
        slug: 'test-blog-post',
        excerpt: 'Test excerpt',
        content: 'Test content',
        featured_image: '',
        status: 'published',
        published_at: '2025-01-01',
        reading_time: 5,
        views_count: 100,
        category_id: 1,
        author_id: 1,
        meta_title: '',
        meta_description: '',
        created_at: '',
        updated_at: '',
        category: null,
        author: null,
      },
    ];

    render(<Home blogPosts={mockBlogPosts} />);
    
    expect(screen.getByText('Latest Insights')).toBeInTheDocument();
    expect(screen.getByTestId('blog-grid')).toBeInTheDocument();
    expect(screen.getByText('Test Blog Post')).toBeInTheDocument();
  });

  it('does not render blog section when no blog posts', () => {
    render(<Home blogPosts={[]} />);
    
    expect(screen.queryByText('Latest Insights')).not.toBeInTheDocument();
  });

  it('renders CTA section', () => {
    render(<Home />);
    
    expect(screen.getByText('Ready to Boost Your Digital Marketing?')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Book Free Consultation/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Get In Touch/i })).toBeInTheDocument();
  });

  it('has correct navigation links', () => {
    render(<Home />);
    
    const consultationLink = screen.getByRole('link', { name: /Book Free Consultation/i });
    const contactLink = screen.getByRole('link', { name: /Get In Touch/i });
    const servicesLink = screen.getByRole('link', { name: /View All Services/i });
    
    expect(consultationLink).toHaveAttribute('href', '/book-consultation');
    expect(contactLink).toHaveAttribute('href', '/contact');
    expect(servicesLink).toHaveAttribute('href', '/services');
  });

  it('renders value propositions', () => {
    render(<Home />);
    
    expect(screen.getByText('Performance Focused')).toBeInTheDocument();
    expect(screen.getByText('Expert Team')).toBeInTheDocument();
    expect(screen.getByText('Advanced Tracking')).toBeInTheDocument();
  });

  it('sets correct page title', () => {
    render(<Home />);
    
    expect(document.title).toBe('ConvertOKit - Meta Ads & Web Analytics Specialist');
  });
});
